const path = require('path');
const CKEditorWebpackPlugin = require('@ckeditor/ckeditor5-dev-webpack-plugin');
const {
  styles
} = require('@ckeditor/ckeditor5-dev-utils');
const UplifyjsPlugin = require("uglifyjs-webpack-plugin");
const compressionWebpackPlugin = require("compression-webpack-plugin");
const pruductionGZipExtensions = ['js', 'css']
module.exports = {
  // publicPath: "/",
  lintOnSave: false, //关闭eslint
  outputDir: process.env.outputDir,
  //outputDir: "dist", 构建时输出目录 默认dist
  //放置静态资源目录
  assetsDir: "static",
  parallel: false,


  // ckeditor5 设置
  // The source of CKEditor is encapsulated in ES6 modules. By default, the code
  // from the node_modules directory is not transpiled, so you must explicitly tell
  // the CLI tools to transpile JavaScript files in all ckeditor5-* modules.
  transpileDependencies: [/ckeditor5-[^/\\]+[/\\]src[/\\].+\.js$/, ],

  configureWebpack: {
    plugins: [
      new compressionWebpackPlugin({
        // [file] 会被替换成原始资源。[path] 会被替换成原始资源的路径， [query] 会被替换成查询字符串。默认值是 "[path].gz[query]"。
        asset: '[path].gz[query]',
        //可以是 function(buf, callback) 或者字符串。对于字符串来说依照 zlib 的算法(或者 zopfli 的算法)。默认值是 "gzip"。
        algorithm: 'gzip',
        //所有匹配该正则的资源都会被处理。默认值是全部资源。
        test: new RegExp('\\.(' + pruductionGZipExtensions.join('|') + ')$'),
        threshold: 10240,
        minRatio: 0.8
      }),

      // CKEditor needs its own plugin to be built using webpack.
      new CKEditorWebpackPlugin({
        language: 'en',
        translationsOutputFile: /app/
      }),
      // new UplifyjsPlugin({
      //   uglifyOptions: {
      //     //生产环境自动删除console
      //     compress: {
      //       drop_debugger: false,
      //       drop_console: true,
      //       pure_funcs: ['console.log']
      //     }
      //   },
      //   sourceMap: false,
      // })
    ],

  },
  chainWebpack: config => {
    // 
    // config.plugin('uglifyjs-plugin').use("uglifyjs-webpack-plugin"),[{

    // }]
    // (1.) To handle editor icons, get the default rule for *.svg files first:
    const svgRule = config.module.rule('svg');
    // * or exclude ckeditor directory from node_modules:
    svgRule.exclude.add(path.join(__dirname, 'node_modules', '@ckeditor'));
    config.module
      .rule('cke-svg')
      .test(/ckeditor5-[^/\\]+[/\\]theme[/\\]icons[/\\][^/\\]+\.svg$/)
      .use('raw-loader')
      .loader('raw-loader');
    // (2.) Transpile the .css files imported by the editor using PostCSS.
    // Make sure only the CSS belonging to ckeditor5-* packages is processed this way.
    config.module
      .rule('cke-css')
      .test(/ckeditor5-[^/\\]+[/\\].+\.css$/)
      .use('postcss-loader')
      .loader('postcss-loader')
      .tap(() => {
        return styles.getPostCssConfig({
          themeImporter: {
            themePath: require.resolve('@ckeditor/ckeditor5-theme-lark'),
          },
          minify: true
        });
      });
  }
};