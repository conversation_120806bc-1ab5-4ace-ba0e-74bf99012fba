{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "installCommand": "npm install --legacy-peer-deps", "buildCommand": "sh build.sh", "rewrites": [{"source": "/property/:id/:subpath", "destination": "https://cnc-odin.vercel.app/property/:id/:subpath"}, {"source": "/property/:id/:subpath/:path*", "destination": "https://cnc-odin.vercel.app/property/:id/:subpath/:path*"}, {"source": "/_next/:path*", "destination": "https://cnc-odin.vercel.app/_next/:path*"}, {"source": "/images/:path*", "destination": "https://cnc-odin.vercel.app/images/:path*"}]}