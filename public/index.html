<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8" />
    <!-- <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0"> -->
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0, maximum-scale=1.0,user-scalable=no"
    />
    <meta
      http-equiv="Content-Security-Policy"
      content="upgrade-insecure-requests"
    />
    <meta
      name="google-signin-client_id"
      content="517653368983-l0um286giqgm2qkeot89f5snksrmbpp3.apps.googleusercontent.com"
    />
    <title>C&C Agent Cloud</title>
    <!-- <link rel="shortcut icon" href="./logo.png" type="image/x-icon"> -->
    <link rel="icon" href="<%= BASE_URL %>logo.png" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Poppins:wght@100;200;300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      html::-webkit-scrollbar {
        width: 0 !important;
      }
      body > .placement-class {
        /* top: 100px !important; */
      }
    </style>
  </head>

  <body>
    <div id="app"></div>
  </body>
  <script
    data-jsd-embedded
    data-key="6ca00c17-ae06-4566-962a-eabdedf674a4"
    data-base-url="https://jsd-widget.atlassian.com"
    src="https://jsd-widget.atlassian.com/assets/embed.js"
  ></script>
  <script>
    (function(apiKey) {
      (function(p, e, n, d, o) {
        var v, w, x, y, z;
        o = p[d] = p[d] || {};
        o._q = o._q || [];
        v = ["initialize", "identify", "updateOptions", "pageLoad", "track"];
        for (w = 0, x = v.length; w < x; ++w)
          (function(m) {
            o[m] =
              o[m] ||
              function() {
                o._q[m === v[0] ? "unshift" : "push"](
                  [m].concat([].slice.call(arguments, 0))
                );
              };
          })(v[w]);
        y = e.createElement(n);
        y.async = !0;
        y.src = "https://cdn.pendo.io/agent/static/" + apiKey + "/pendo.js";
        z = e.getElementsByTagName(n)[0];
        z.parentNode.insertBefore(y, z);
      })(window, document, "script", "pendo");
      pendo.initialize({
        visitor: {
          id: "VISITOR-UNIQUE-ID",
        },

        account: {
          id: "ACCOUNT-UNIQUE-ID",
        },
      });
    })("00b7566f-2f34-448f-497d-501871adce32");
  </script>
  <!-- 第三方检测  // "ly-tab": "^2.1.2" monica.haoyi, "vue": "^2.5.2",-->
</html>
