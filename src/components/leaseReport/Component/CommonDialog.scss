@import "../../../css/dialog.scss";
@import "../../../css/button.scss";
.content {
  margin-top: 20px;
  ::v-deep .el-form-item {
    .el-form-item__label {
      text-align: left;
    }
    .el-input__inner {
      padding-left: 40px;
    }
    .el-input{
      width: 100%;
    }
    .el-input__prefix {
      display: inline-block;
      width: 30px;
      height: calc(100% - 4px);
      background-color: #f4f9fc;
      margin: 2px 0 0 0;
    }
    .el-date-editor .el-input__prefix{
      background-color: #fff;
    }
  }
  ::v-deep input[type="number"] {
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  -moz-appearance: textfield;
  }
}