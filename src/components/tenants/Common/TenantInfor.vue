<template>
  <div class="tenant-box">
    <div class="addtenant_contents">
      <el-form
        label-position="right"
        label-width="130px"
        :rules="rules"
        ref="contactForm"
        :model="data"
      >
        <div class="contact_both_side">
          <div class="contact_side_left">
            <span class="contact_tip">Primary Contact</span>
            <el-form-item label="First Name" prop="first_name">
              <el-input v-model="data.first_name"></el-input>
            </el-form-item>
            <el-form-item label="Last Name" prop="last_name">
              <el-input v-model="data.last_name"></el-input>
            </el-form-item>
            <el-form-item label="Email Address" prop="email1">
              <el-input v-model="data.email1" type="email"></el-input>
            </el-form-item>
            <el-form-item label="Email Address 2" prop="email2">
              <el-input v-model="data.email2"></el-input>
            </el-form-item>
            
            <el-form-item label="Cell Phone">
              <el-input
                v-model="data.cell_phone"
                maxlength="22"
                @input="e=>data.cell_phone=changePhone(e)"
              ></el-input>
            </el-form-item>
            <el-form-item label="Work Phone">
              <el-input
                v-model="data.work_phone"
                maxlength="22"
                @input="e=>data.work_phone=changePhone(e)"
              ></el-input>
            </el-form-item>
            <el-form-item label="Street Address">
              <el-input v-model="data.street1"></el-input>
            </el-form-item>
            <el-form-item label="Street Address2">
              <el-input v-model="data.street2"></el-input>
            </el-form-item>
            <el-form-item label="City">
              <el-input v-model="data.city"></el-input>
            </el-form-item>
            <div class="side_special">
              <el-form-item label="State">
                <el-input v-model="data.state"></el-input>
              </el-form-item>
              <el-form-item label="Zip Code" class="demo-form-inline">
                <el-input v-model="data.zip"></el-input>
              </el-form-item>
            </div>
            <el-form-item label="Country">
              <el-input v-model="data.country"></el-input>
            </el-form-item>
          </div>
          <div class="contact_side_right">
            <p class="disflet_lease">
              <span class="contact_tip">Secondary Contact</span>
              <slot name="market"></slot>
            </p>
            <el-form-item label="First Name">
              <el-input v-model="data.sec_first_name"></el-input>
            </el-form-item>
            <el-form-item label="Last Name">
              <el-input v-model="data.sec_last_name"></el-input>
            </el-form-item>
            <el-form-item label="Email Address" prop="sec_email1">
              <el-input v-model="data.sec_email1"></el-input>
            </el-form-item>
            <el-form-item label="Email Address 2" prop="sec_email2">
              <el-input v-model="data.sec_email2" type="email"></el-input>
            </el-form-item>
            <el-form-item label="Cell Phone">
              <el-input
                v-model="data.sec_cell_phone"
                maxlength="22"
                @input="e=>data.sec_cell_phone=changePhone(e)"
              ></el-input>
            </el-form-item>
            <el-form-item label="Work Phone">
              <el-input
                v-model="data.sec_work_phone"
                maxlength="22"
                @input="e=>data.sec_work_phone=changePhone(e)"
              ></el-input>
            </el-form-item>
            <el-form-item label="Street Address">
              <el-input v-model="data.sec_street1"></el-input>
            </el-form-item>
            <el-form-item label="Street Address2">
              <el-input v-model="data.sec_street2"></el-input>
            </el-form-item>
            <el-form-item label="City">
              <el-input v-model="data.sec_city"></el-input>
            </el-form-item>
            <div class="side_special">
              <el-form-item label="State">
                <el-input v-model="data.sec_state"></el-input>
              </el-form-item>
              <el-form-item label="Zip Code" class="demo-form-inline">
                <el-input v-model="data.sec_zip"></el-input>
              </el-form-item>
            </div>

            <el-form-item label="Country">
              <el-input v-model="data.sec_country"></el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <div class="btn">
      <el-button class="Add_btn" @click="save">Save</el-button>
    </div>
  </div>
</template>
<script>
import { Minixs } from "../../../common/mixins";
export default {
  mixins: [Minixs],
  props: ["data"],
  data() {
    return {
      rules: {
        first_name: [
          {
            required: true,
            message: "this information is required",
            trigger: ["blur","change"],
          },
        ],
        last_name: [
          {
            required: true,
            message: "this information is required",
            trigger: ["blur","change"],
          },
        ],
        email1: [
          {
            required: true,
            message: "this information is required",
            trigger: ["blur","change"],
          },
          {
            type: "email",
            message: "Please input correct email address",
            trigger: ["blur","change"],
          },
        ],
        email2: [
          {
            type: "email",
            message: "Please input correct email address",
            trigger: ["blur","change"],
          },
        ],
        sec_email1: [
          {
            type: "email",
            message: "Please input correct email address",
            trigger: ["blur","change"],
          },
        ],
        sec_email2: [
          {
            type: "email",
            message: "Please input correct email address",
            trigger: ["blur","change"],
          },
        ],
      },
    };
  },
  methods: {
    save() {
      this.$refs.contactForm.validate((valid) => {
        if (valid) {
          this.$emit("save",this.data)
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.addtenant_contents {
  padding: 0 20px 40px;
  .contact_title {
    ::v-deep .el-form-item__content {
      text-align: left;
    }
  }
  // 表单数据
  .contact_both_side {
    display: flex;
    justify-content: space-between;
    display: -webkit-flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    ::v-deep .el-input {
      width: 365px;
    }
    .contact_tip {
      text-align: left;
      display: inline-block;
      padding: 0 0 20px;
      width: 100%;
    }
    .side_special {
      display: flex;
      justify-content: flex-start;
      display: -webkit-flex;
      -webkit-flex-wrap: wrap;
      flex-wrap: wrap;
      ::v-deep .el-input {
        width: 160px;
      }
      .demo-form-inline {
        width: 85px;
        ::v-deep .el-form-item__label {
          width: 90px !important;
        }
        ::v-deep .el-form-item__content {
          margin-left: 90px !important;
          .el-input {
            width: 111px;
          }
        }
      }
    }
    @media (max-width: 560px) {
      .contact_side_left,
      .contact_side_right {
        width: 100%;
      }
      ::v-deep .el-input {
        width: 100%;
      }
      .side_special {
        display: block;
        ::v-deep .el-input {
          width: 100%;
        }
        .demo-form-inline {
          width: 100%;
          ::v-deep .el-form-item__label {
            width: 140px !important;
          }
          ::v-deep .el-form-item__content {
            margin-left: 140px !important;
            .el-input {
              width: 100%;
            }
          }
        }
      }
    }
  }
  .send_contant {
    ::v-deep .el-radio-button {
      margin: 0px 20px 20px 0;
      border-radius: 4px;
    }
    ::v-deep .is-active {
      background: #678993;
    }
    ::v-deep .el-form-item.is-required:not(.is-no-asterisk)
      .el-form-item__label-wrap
      > .el-form-item__label:before,
    ::v-deep .el-form-item.is-required:not(.is-no-asterisk)
      > .el-form-item__label:before {
      display: none;
    }
    ::v-deep .jw-container {
      margin-top: 0;
      background-color: #fff;
      border: 1px solid #e5e5e5;
      padding-left: 10px;
      height: 35px;
    }
    ::v-deep .point-container .place-hold {
      margin-top: 6px;
    }
    .contant_type_row {
      display: flex;
      justify-content: flex-start;
      display: -webkit-flex;
      -webkit-flex-wrap: wrap;
      flex-wrap: wrap;
      ::v-deep .el-checkbox-button {
        margin: 0px 20px 20px 0;
        border-radius: 4px;
      }

      ::v-deep .el-checkbox-button__inner {
        border: none;
        background: #f2f2f2;
        border-radius: 4px;
        width: 140px;
        width: 250px;
      }

      ::v-deep .el-checkbox-button .el-checkbox-button__inner:hover {
        color: #333;
      }

      ::v-deep .el-checkbox-button.is-checked .el-checkbox-button__inner:hover {
        color: #fff;
      }

      ::v-deep .el-checkbox-button.is-checked .el-checkbox-button__inner {
        background-color: #678993;
        box-shadow: -0.017857rem 0 0 0 #678993;
      }
      ::v-deep .el-checkbox-button__inner {
        width: 100px;
      }
    }
  }
  .market_email {
    ::v-deep .el-checkbox__inner {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      &::after {
        display: none;
      }
    }
    ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
    ::v-deep .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-image: url("../../../assets/img/true.png");
      background-size: cover;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      border-color: #678993;
    }
    ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #333;
    }
  }
  .disflet_lease {
    display: flex;
    margin: 0;
    justify-content: flex-start;
    /* align-items: center; */
    input[type="radio"]:checked + .radio {
      background: url("../../../assets/img/true.png") no-repeat left top; /*被选中时，换背景图片*/
      background-size: 20px 20px;
    }
  }
  .market_type {
    ::v-deep .el-radio {
      margin-bottom: 5px;
      .el-radio__input .el-radio__inner {
        width: 20px;
        height: 20px;
        &::after {
          display: none;
        }
        &:hover {
          border-color: #678993;
        }
      }
      .el-radio__input.is-checked .el-radio__inner {
        background-color: #fff;
        background-image: url("../../../assets/img/true.png");
        background-size: cover;
        border: none;
      }
      .el-radio__input.is-checked + .el-radio__label {
        color: #666;
      }
    }
  }
}
.btn .Add_btn {
  width: 300px;
  background-color: #678993;
  color: #ffffff;
  font-size: 20px;
  margin-bottom: 30px;
  &:hover {
    background-color: #57737b;
  }
}
</style>