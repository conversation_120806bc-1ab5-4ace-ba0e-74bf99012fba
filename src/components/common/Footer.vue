<template>
  <div style="margin-top:30px;" class="footer_box">
    <div class="footer">
      <div class="outer-most">
        <div class="outerBig">
          <el-button @click="dialogfooter = true">Report Issue / Submit Request</el-button>
        </div>
      </div>
    </div>
    <!--弹出框 Principal Broker = <EMAIL>
    Office Manager = <EMAIL>-->
    <el-dialog title="Report issue / submit request" :visible.sync="dialogfooter" width="480px">
      <div class="footer_contant">
        <el-form :model="data">
          <el-form-item>
            <div class="select_send">
              <span style="margin-bottom:10px;">Send to:</span>
              <el-radio-group v-model="sendType">
                <el-radio label="1">Office Manager</el-radio>
                <el-radio label="2">Principal Broker</el-radio>
                <el-radio label="3">Both</el-radio>
              </el-radio-group>
            </div>
          </el-form-item>
          <el-form-item>
            <p class="footer_tip">Subject(eg Page or Property)</p>
            <el-input placeholder="Please input" v-model="data.subject"></el-input>
          </el-form-item>
          <el-form-item>
            <p class="footer_tip">Description</p>
            <el-input
              type="textarea"
              :autosize="{ minRows: 6}"
              placeholder="Please input"
              resize="none"
              v-model="data.desc"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="start">SUBMIT</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "footerConpent",
  data() {
    return {
      data: {},
      dialogfooter: false,
      sendType: "1"
    };
  },
  methods: {
    // 发送email
    start() {
      var root = process.env.VUE_APP_MODE;
      let tos = [];
      if (root == "development") {
        // 测试
        if (this.sendType == 1) {
          tos = ["<EMAIL>"];
        } else if (this.sendType == 2) {
          tos = ["<EMAIL>"];
        } else {
          tos = ["<EMAIL>", "<EMAIL>"];
        }
      } else {
        if (this.sendType == 1) {
          tos = ["<EMAIL>"];
        } else if (this.sendType == 2) {
          tos = ["<EMAIL>"];
        } else {
          tos = [
            "<EMAIL>",
            "<EMAIL>"
          ];
        }
      }

      // sendEmail(this.data.subject, this.data.desc, tos.join(",")).then(
      //   res => {
      //     this.$message.success("Email send successfully");
      //     // 发送成功，清空数据
      //     this.dialogfooter = false;
      //     this.data.tos = ["1"];
      //     this.attachments = [];
      //     this.data.ccs = this.data.subject = this.data.desc = this.fileName =
      //       "";
      //   },
      //   err => {
      //     this.$message.error("Email send fail, please try again later");
      //   }
      // );
    }
  }
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 40px 0;
  background-color: #ffffff;
  .outer-most {
    margin: 0 auto;
    width: 80%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    span {
      display: inline-block;
      margin-right: 50px;
      font-size: 16px;
    }
  }
  @media (max-width: 772px) {
    .outer-most {
      justify-content: center;
    }
  }
}
.footer_box {
  ::v-deep .el-button {
    border: none;
    background-color: #678993;
    color: #ffffff;
    font-size: 20px;
  }
  ::v-deep .el-dialog__header {
    text-align: left;
    background-color: #678993;
  }
  ::v-deep .el-dialog__headerbtn .el-dialog__close,
  ::v-deep .el-dialog__title {
    color: #fff;
  }
}
.footer_tip {
  text-align: left;
  margin: 0px;
}
.select_send {
  text-align: left;
  ::v-deep .el-radio__input.is-checked .el-radio__inner {
    border: none;
    background-image: url("../../assets/img/true.png");
    background-size: cover;
  }
  ::v-deep .el-radio__inner {
    width: 21px;
    height: 21px;
    &::after {
      display: none;
    }
  }
  ::v-deep .el-radio__input.is-checked + .el-radio__label {
    color: #606266;
  }
}
</style>
