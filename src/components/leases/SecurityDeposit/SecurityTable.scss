.payment-title {
  text-align: left;
  font-family: "Roboto-Bold", sans-serif;
  font-size: 18px;
  color: #36425d;
}
.table {
  table {
    tr {
      font-size: 14px;
      color: #666;
      font-family: "Roboto-Regular", sans-serif;
      border-bottom: 1px solid #e1e9ef;
      text-align: left;

      th {
        text-align: center;
        font-size: 14px;
        color: #737a8c;
        font-family: "Roboto-Medium", sans-serif;
        padding: 17.5px 0;
        text-align: left;
      }

      td {
        padding: 17.5px 0;
        font-family: "Roboto-Regular", sans-serif;
        font-size: 14px;
        color: #38425b;
        text-align: left;
      }

      &:hover {
        background-color: rgb(245, 247, 250);
      }
    }

    .table_top {
      border-top: 1px solid #e1e9ef;
    }

    .lb_color {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 10px;
    }
  }

  @media screen and (max-width: 1000px) {
    overflow: scroll;
  }
}
