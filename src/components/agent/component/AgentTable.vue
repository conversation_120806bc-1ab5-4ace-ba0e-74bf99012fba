<template>
  <div class="agent-table">
    <div class="table">
      <el-table :data="this.data" style="width: 100%" :cell-style="{padding: '10px 0px', 'vertical-align':'center'}">
        <el-table-column label="AVATAR" prop="date" width="90">
          <template slot-scope="props">
            <p class="agent_avatar" @click="handleEdit(props.row)">
              <img v-if="props.row.avatar" :src="props.row.avatar" alt />
              <img v-else src="../../../assets/img/person.png" alt />
            </p>
          </template>
        </el-table-column>
        <el-table-column label="NAME" min-width="120px">
          <template slot-scope="props">
            <div class="d-flex align-center">
              <p @click="handleEdit(props.row)"> {{ props.row.first_name }} {{ props.row.last_name }} </p>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="EMAIL" min-width="120px">
          <template slot-scope="scope">
            <p @click="handleEdit(scope.row)">{{ scope.row.email }}</p>
          </template>
        </el-table-column>
        <el-table-column label="PHONE" min-width="150">
          <template slot-scope="scope">
            <p @click="handleEdit(scope.row)">{{ scope.row.phone }}</p>
          </template>
        </el-table-column>
        <el-table-column  label="ACTIVATION" min-width="100px" prop="agentcommission" >
          <template slot-scope="scope">
            <el-switch  @change="changeActivateValue($event, scope.$index, scope.row)" v-model="scope.row.is_active" active-color="#678993" inactive-color="#737A8C" ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="TYPE" width="120">
          <template slot-scope="scope">
            <span class="type-box" v-if="!scope.row.is_admin "> Agent </span>
            <span class="type-box" v-else-if="scope.row.is_admin"> Admin </span>
          </template>
        </el-table-column>
        <el-table-column width="60">
          <template slot-scope="scope">
            <i class="el-icon-edit-outline" @click="handleEdit(scope.row)" ></i>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Page :total="total" :pageSize="25" @number="pagesChanged"></Page>
  </div>
</template>
<script>
import {  apiUpdateUser, } from "../../../API/api";
import Page from "../../common/Paging";
export default {
  components: { Page },
  props: ["data","total"],
  methods: {
    handleEdit(item) {
      this.$emit("editAgent",item);
    },
    pagesChanged(number){
      this.$emit("pagesChanged",number)
    },
    // 授权
    changeActivateValue(val, index, row) {
      let obj = {
        is_active: row.is_active,
      };
      apiUpdateUser(row.user_id, obj).then((res) => {
          this.$message.success("success");
        }).catch((error) => {
           let a = this.data[index];
          a.is_active = !val;
          this.$set(this.data, index, a);
          this.$message.error(error.response.data.detail?error.response.data.detail:"fail");
        });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../../css/table.scss";
.agent-table {
  .agent_avatar {
    width: 40px;
    border-radius: 50%;
    height: 40px;
    margin: 0;
    img {
      width: 40px;
      border-radius: 50%;
      height: 40px;
      object-fit: cover;
    }
  }
  .type-box {
    border: 1px solid #e1e9ef;
    background-color: #f4f9fc;
    padding: 4px 20px;
    border-radius: 2px;
    display: inline-block;
    color: #38425b;
  }
  .el-icon-edit-outline {
    font-size: 22px;
    color: #678993;
  }
}
</style>