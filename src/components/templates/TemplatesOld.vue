<template>
  <div class="schedule_box">
    <!-- Real Estate -->
    <Tempaple-Component
      :data="real_estate"
      category="real_estate"
      self="false"
      @uploadFile="realEstate"
      @closeTag="closeTag"
    >
      <template slot="topName">REAL ESTATE</template>
      <template slot="btn">
        <el-button :disabled="$store.state.userInfo.is_admin === false">Upload</el-button>
      </template>
    </Tempaple-Component>
    <!-- Rentals -->
    <Tempaple-Component
      :data="rental"
      category="rentals"
      self="false"
      @uploadFile="rentals"
      @closeTag="closeTag"
    >
      <template slot="topName">RENTAL</template>
      <template slot="btn">
        <el-button :disabled="$store.state.userInfo.is_admin === false">Upload</el-button>
      </template>
    </Tempaple-Component>
    <!-- Company information -->
    <Tempaple-Component
      :data="company"
      category="company"
      self="false"
      @uploadFile="companyInformation"
      @closeTag="closeTag"
    >
      <template slot="topName">COMPANY INFORMTION</template>
      <template slot="btn">
        <el-button :disabled="$store.state.userInfo.is_admin === false">Upload</el-button>
      </template>
    </Tempaple-Component>
    <!-- JOYCE’S DOCUMENTS-->
    <Tempaple-Component
      :data="own"
      category="self"
      self="true"
      @uploadFile="selfDocuments"
      @closeTag="closeTag"
    >
      <template
        slot="topName"
      >{{$store.state.userInfo.first_name?$store.state.userInfo.first_name.toUpperCase():"" }} {{$store.state.userInfo.last_name?$store.state.userInfo.last_name.toUpperCase():""}}'S DOCUMENTS</template>
      <template slot="btn">
        <el-button>Upload</el-button>
      </template>
    </Tempaple-Component>
  </div>
</template>
<script>
import TempapleComponent from "./Component/TempalteComponent";
import { apiGetTemplatels, apiDelTemplate } from "../../API/api";
function sortByKey(array, key) {
  return array.sort(function(a, b) {
    var x = a[key].toLowerCase();
    var y = b[key].toLowerCase();
    if (x < y) return -1;
    if (x > y) return 1;
    return 0;
  });
}
export default {
  components: { TempapleComponent },
  data() {
    return {
      all: [],
      real_estate: [],
      rental: [],
      company: [],
      own: []
    };
  },
  methods: {
    // Real Estate
    realEstate(res) {
      this.real_estate.push(res);
      sortByKey(this.real_estate, "file_name");
    },
    // Rentals
    rentals(res) {
      this.rental.push(res);
      sortByKey(this.rental, "file_name");
    },
    //Company information
    companyInformation(res) {
      this.company.push(res);
      sortByKey(this.company, "file_name");
    },
    selfDocuments(res) {
      this.own.push(res);
      sortByKey(this.own, "file_name");
    },
    // 处理模板的列表
    Handle() {
      this.all.forEach((item, index) => {
        if (item.category == "real_estate") {
          this.real_estate.push(item);
        }
        if (item.category == "rentals") {
          this.rental.push(item);
        }
        if (item.category == "company") {
          this.company.push(item);
        }
        if (item.category == "self") {
          this.own.push(item);
        }
      });
    },
    // 删除列表
    closeTag(val) {
      apiDelTemplate(val.template_file_uuid)
        .then(res => {
          if (val.category == "real_estate") {
            this.real_estate.forEach((item, index) => {
              if (item.template_file_uuid == val.template_file_uuid) {
                this.real_estate.splice(index, 1);
                return true;
              }
            });
          }
          if (val.category == "rentals") {
            this.rental.forEach((item, index) => {
              if (item.template_file_uuid == val.template_file_uuid) {
                this.rental.splice(index, 1);
                return true;
              }
            });
          }

          if (val.category == "company") {
            this.company.forEach((item, index) => {
              if (item.template_file_uuid == val.template_file_uuid) {
                this.company.splice(index, 1);
                return true;
              }
            });
          }

          if (val.category == "self") {
            this.own.forEach((item, index) => {
              if (item.template_file_uuid == val.template_file_uuid) {
                this.own.splice(index, 1);
                return true;
              }
            });
          }
        })
        .catch(err => {
          this.$message.error("Delete failed");
        });
    }
  },
  created() {
    apiGetTemplatels().then(res => {
      this.all = res.results;
      this.Handle();
    });
  }
};
</script>
