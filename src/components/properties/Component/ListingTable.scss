.w-table {
  background-color: #fff;
  margin-top: 10px;

  /* 滚动条的样式 */
  /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
  ::-webkit-scrollbar {
    width: 6px;
    /*滚动条宽度*/
    height: 8px;
    /*滚动条高度*/
    background-color: white;
    // -webkit-overflow-scrolling:touch;overflow:auto!important;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 0 0 0px white;
    background-color: rgb(193, 193, 193);
    /*滚动条的背景颜色*/
    border-radius: 30px;
  }
  /* 对上下滚动条的单独修改 */
  .el-table__body-wrapper::-webkit-scrollbar {
    /*width: 0;宽度为0隐藏*/
    // -webkit-overflow-scrolling:touch;overflow:auto!important;
    width: 0px;
  }

  /* .el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 2px;
  height: 50px;
  background: #eee;
}
.el-table__body-wrapper::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  background: rgba(0, 0, 0, 0.4);
} */
  .el-table {
    th {
    padding: 10px 0;
    .virtual {
      position: fixed;
      display: block;
      width: 0;
      height: 0;
      margin-left: -10px;
      background: none;
      border: none;
    }

    &.darg_active_left {
      .virtual {
        border-left: 2px dotted #666;
        z-index: 9;
      }
    }

    &.darg_active_right {
      .virtual {
        border-right: 2px dotted #666;
        z-index: 9;
      }
    }
  }
  .darg_start {
    background-color: #fff;
  }
    background-color: #fff;
  .red{background-color: rgba(255, 0, 0,.1) !important;}
  .blue{background-color: rgba(30,144,255, 0.1) !important;}
  .green{background-color: rgba(55,160,0,.1) !important;}
}
  .el-table__body-wrapper{
    height: 98vh !important;
    -webkit-overflow-scrolling:touch;overflow-y:auto!important;
    @media (max-width:1400px) {
      height: 98vh !important;
    }
  }
  .thead-cell {
    padding: 0;
    display: inline-flex;
    flex-direction: column;
    align-items: left;
    cursor: pointer;
    overflow: initial;
    &:before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
    }
  }
}
.w-table_moving {
  .el-table th .thead-cell {
    cursor: move !important;
  }
  .el-table__fixed {
    cursor: not-allowed;
  }
}
 .show-page {
  text-align: left;
  margin-right: 20px;
}
