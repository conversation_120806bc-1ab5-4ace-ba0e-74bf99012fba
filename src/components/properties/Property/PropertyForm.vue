<template>
  <div class="property-form">
    <div class="propety">
      <div style="min-height: 600px">
        <el-form :model="data" ref="ListingForm" :rules="rules">
          <div class="pages">
            <div class="form_in">
              <!-- Location Details -->
              <div>
                <div class="top-prodect" @click="locationShow = !locationShow">
                  <span class="title-text">Location Details</span
                  ><i class="el-icon-arrow-down" v-if="locationShow"></i>
                  <i class="el-icon-arrow-right" v-if="!locationShow"></i>
                </div>
                <div v-if="locationShow">
                  <div class="set_width">
                    <div class="propety_row">
                      <div class="propety_type">
                        <el-form-item prop="address" :rules="rules.required">
                          <p class="propety-title">Property Address</p>
                          <el-input v-model="data.address"></el-input>
                        </el-form-item>
                      </div>
                      <div class="propety_type">
                        <el-form-item prop="area" :rules="rules.required">
                          <p class="propety-title">Area</p>
                          <el-select
                            v-model="data.area"
                            placeholder="Please choose"
                          >
                            <el-option
                              v-for="item in areas"
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                  <div class="check-row row">
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.waterfront"
                        >Waterfront</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.water_view"
                        >Water Views</el-checkbox-button
                      >
                    </div>
                  </div>
                  <div class="set_width">
                    <div class="propety_row">
                      <div class="propety_type">
                        <el-form-item prop="distance_to_beach">
                          <p class="propety-title">
                            Distance to Nearest Beach (miles)
                          </p>
                          <el-input
                            v-model="data.distance_to_beach"
                            placeholder
                          ></el-input>
                        </el-form-item>
                      </div>
                      <div class="propety_type">
                        <el-form-item prop="distance_to_the_hub">
                          <p class="propety-title">
                            Distance to C&C Office (miles)
                          </p>
                          <el-input
                            v-model="data.distance_to_the_hub"
                            placeholder
                          ></el-input>
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Descriptions -->
              <div>
                <div class="top-prodect" @click="decsShow = !decsShow">
                  <span class="title-text">Descriptions</span
                  ><i class="el-icon-arrow-down" v-if="decsShow"></i>
                  <i class="el-icon-arrow-right" v-if="!decsShow"></i>
                </div>
                <div v-if="decsShow">
                  <div class="set_width">
                    <el-form-item prop="headline" :rules="rules.required">
                      <p class="propety-title">
                        Headline (required for some websites)
                      </p>
                      <el-input v-model="data.headline" placeholder></el-input>
                    </el-form-item>
                    <el-form-item prop="description" :rules="rules.required">
                      <p class="propety-title">Main Description</p>
                      <el-input
                        v-model="data.description"
                        type="textarea"
                        resize="none"
                        :autosize="{ minRows: 1 }"
                      ></el-input>
                    </el-form-item>
                    <el-form-item>
                      <p class="propety-title">First Floor</p>
                      <el-input
                        v-model="data.first_floor"
                        type="textarea"
                        resize="none"
                        :autosize="{ minRows: 1 }"
                      ></el-input>
                    </el-form-item>
                    <el-form-item>
                      <p class="propety-title">Second Floor</p>
                      <el-input
                        v-model="data.second_floor"
                        type="textarea"
                        resize="none"
                        :autosize="{ minRows: 1 }"
                      ></el-input>
                    </el-form-item>
                    <el-form-item>
                      <p class="propety-title">Lower Level</p>
                      <el-input
                        v-model="data.lower_level"
                        type="textarea"
                        resize="none"
                        :autosize="{ minRows: 1 }"
                      ></el-input>
                    </el-form-item>
                    <el-form-item>
                      <p class="propety-title">Cottage / Other Structure</p>
                      <el-input
                        v-model="data.other_structure"
                        type="textarea"
                        resize="none"
                        :autosize="{ minRows: 1 }"
                      ></el-input>
                    </el-form-item>
                  </div>
                </div>
              </div>
              <!-- General Information  -->
              <div>
                <div class="top-prodect" @click="generalShow = !generalShow">
                  <span class="title-text">General Information</span
                  ><i class="el-icon-arrow-down" v-if="generalShow"></i>
                  <i class="el-icon-arrow-right" v-if="!generalShow"></i>
                </div>
                <div v-if="generalShow">
                  <div class="set_width" style="">
                    <div class="propety_row" style="margin-botton: 20px">
                      <div class="propety_type">
                        <el-form-item prop="key_number">
                          <p class="propety-title">Key Number</p>
                          <el-input v-model="data.key_number" />
                        </el-form-item>
                      </div>
                      <div class="propety_type">
                        <el-form-item>
                          <p class="propety-title">Listing Priority</p>
                          <el-select
                            v-model="data.priority"
                            placeholder="Please choose"
                          >
                            <el-option
                              v-for="item in propertyPriorityList"
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>
                      <div class="propety_type">
                        <el-form-item prop="capacity" :rules="rules.required">
                          <p class="propety-title">Max Sleeping Capacity</p>
                          <el-select
                            v-model="data.capacity"
                            placeholder="Please choose"
                          >
                            <el-option
                              v-for="item in 100"
                              :key="item"
                              :label="item"
                              :value="item"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>
                      <div class="propety_type">
                        <p class="propety-title">House Phone Number</p>
                        <el-input
                          v-model="data.house_phone"
                          type="tel"
                          maxlength="22"
                          @input="(e) => (data.house_phone = changePhone(e))"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="check-row row">
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.air_conditioning"
                        >Air Conditioning</el-checkbox-button
                      >
                      <div v-show="data.air_conditioning" class="show-margin">
                        <p class="propety-title">A/C Type</p>
                        <el-select
                          v-model="data.ac_types"
                          multiple
                          placeholder="Please choose"
                        >
                          <el-option
                            v-for="item in acTypeList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                          ></el-option>
                        </el-select>
                      </div>
                    </div>

                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.clothes_dryer"
                        >Dryer (clothes)</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.fan"
                        >Fans</el-checkbox-button
                      >
                      <div v-show="data.fan" class="show-margin">
                        <p class="propety-title">Quantity</p>
                        <el-select v-model="data.fan_quantity" placeholder>
                          <el-option
                            v-for="item in 100"
                            :key="item"
                            :label="item"
                            :value="item"
                          >
                          </el-option>
                        </el-select>
                      </div>
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.fireplace"
                        >Fireplaces (working)</el-checkbox-button
                      >
                      <div v-show="data.fireplace" class="show-margin">
                        <p class="propety-title">Quantity</p>
                        <el-select
                          v-model="data.fireplace_quantity"
                          placeholder=""
                        >
                          <el-option
                            v-for="item in 100"
                            :key="item"
                            :label="item"
                            :value="item"
                          >
                          </el-option>
                        </el-select>
                      </div>
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.gym"
                        >Fitness Room / Gym</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.hair_dryer"
                        >Hair Dryer</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.heating"
                        >Heating</el-checkbox-button
                      >
                      <div v-show="data.heating" class="show-margin">
                        <p class="propety-title">Heating Type</p>
                        <el-select
                          v-model="data.heating_type"
                          placeholder="Please choose"
                        >
                          <el-option
                            v-for="item in heatingTypeList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                          ></el-option>
                        </el-select>
                      </div>
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.iron"
                        >Iron</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.ironing_board"
                        >Ironing Board</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.jacuzzi"
                        >Jacuzzi</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.linen"
                        >Linens Provided</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.parking"
                        >Off-street Parking</el-checkbox-button
                      >
                      <div v-show="data.parking" class="show-margin">
                        <p class="propety-title">Quantity</p>
                        <el-select
                          v-model="data.parking_quantity"
                          placeholder=""
                        >
                          <el-option
                            v-for="item in 100"
                            :key="item"
                            :label="item"
                            :value="item"
                          >
                          </el-option>
                        </el-select>
                      </div>
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.washing_machine"
                        >Washing Machine</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.wifi"
                        >Wifi</el-checkbox-button
                      >
                      <div v-show="data.wifi" class="show-margin">
                        <p class="propety-title">Wifi Network</p>
                        <el-input
                          v-model="data.wifi_network_name"
                          placeholder
                          oninput="value=value.replace(/[\W]/g,'')"
                        ></el-input>
                      </div>
                      <div v-show="data.wifi" class="show-margin">
                        <el-form-item prop="wifi_password">
                          <p class="propety-title">Wifi Password</p>
                          <el-input
                            v-model="data.wifi_password"
                            placeholder
                            oninput="value=value.replace(/[\W]/g,'')"
                          ></el-input>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.safe"
                        >Safe</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.towel"
                        >Towels Provided</el-checkbox-button
                      >
                    </div>
                  </div>
                  <div class="set_width">
                    <div class="propety_row">
                      <div class="propety_type">
                        <el-form-item>
                          <p class="propety-title">
                            MA Occupancy Tax Certificate Number
                          </p>
                          <el-input
                            v-model="data.occupancy_tax_number"
                            placeholder
                            oninput="value=value.replace(/[\W]/g,'')"
                          ></el-input>
                        </el-form-item>
                      </div>
                      <div class="propety_type">
                        <div class="propety_row">
                          <div class="propety_type">
                            <el-form-item prop="year_built">
                              <p class="propety-title">Year Built</p>
                              <el-input
                                v-model="data.year_built"
                                placeholder
                                type="number"
                                maxlength="4"
                              ></el-input>
                            </el-form-item>
                          </div>
                          <div class="propety_type">
                            <el-form-item prop="year_renovated">
                              <p class="propety-title">Year Renovated</p>
                              <el-input
                                v-model="data.year_renovated"
                                placeholder
                                type="number"
                                maxlength="4"
                              ></el-input>
                            </el-form-item>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="propety_type">
                      <p class="upload_file">
                        <el-upload
                          class="upload-demo"
                          action="https://devapi.booknantucket.com/upload/file"
                          :headers="uploadHeader"
                          :show-file-list="false"
                          :on-success="
                            (response, file, fileList) => {
                              return onSuccess(response, file, fileList);
                            }
                          "
                        >
                          <el-button slot="trigger" size="small" type="primary">
                            <img src="../../assets/img/file.png" alt />
                          </el-button>
                        </el-upload>
                        <span
                          class="tip"
                          :style="{
                            color:
                              fileName == 'Attached Files' ? '#222' : '#678993',
                          }"
                          >{{ fileName }}</span
                        >
                      </p>
                    </div>
                    <div class="propety_row">
                      <div class="propety_type">
                        <el-form-item prop="other_rental_firms">
                          <p class="propety-title">Other Rental Firms</p>
                          <el-select
                            v-model="data.other_rental_firms"
                            placeholder="Please choose"
                            multiple
                            filterable
                            default-first-option
                          >
                            <el-option
                              v-for="item in companyList"
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Dining -->
              <div>
                <div class="top-prodect" @click="diningShow = !diningShow">
                  <span class="title-text">Dining</span
                  ><i class="el-icon-arrow-down" v-if="diningShow"></i>
                  <i class="el-icon-arrow-right" v-if="!diningShow"></i>
                </div>
                <div v-if="diningShow">
                  <div class="check-row row">
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.dining_area"
                        >Dining Area</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.outdoor_dining_area"
                        >Outdoor Dining Area</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.counter_seating"
                        >Counter Seating</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.dining_room"
                        >Dining Room</el-checkbox-button
                      >
                    </div>
                  </div>
                  <div class="set_width">
                    <div class="propety_row">
                      <div class="propety_type">
                        <el-form-item prop="seating" :rules="rules.required">
                          <p class="propety-title">Seating for</p>
                          <el-select
                            v-model="data.seating"
                            placeholder="Please choose"
                          >
                            <el-option
                              v-for="item in 99"
                              :key="item"
                              :label="item"
                              :value="item"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Entertainment -->
              <div>
                <div
                  class="top-prodect"
                  @click="entertainShow = !entertainShow"
                >
                  <span class="title-text">Entertainment</span
                  ><i class="el-icon-arrow-down" v-if="entertainShow"></i>
                  <i class="el-icon-arrow-right" v-if="!entertainShow"></i>
                </div>
                <div v-if="entertainShow">
                  <div class="check-row row">
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.bluetooth_speaker"
                        >Bluetooth Speakers</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.book"
                        >Books</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.dvd_player"
                        >DVD player</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.tv"
                        >TV</el-checkbox-button
                      >
                      <div class="show-margin" v-show="data.tv">
                        <p class="propety-title">TV Service</p>
                        <el-select
                          v-model="data.tv_service"
                          placeholder="Please choose"
                        >
                          <el-option
                            v-for="item in tvServiceList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                          ></el-option>
                        </el-select>
                      </div>
                      <div class="show-margin" v-show="data.tv">
                        <p class="propety-title">TV Number</p>
                        <el-select v-model="data.tv_quantity"
                          ><el-option
                            v-for="item in 100"
                            :key="item"
                            :label="item"
                            :value="item"
                          >
                          </el-option
                        ></el-select>
                      </div>
                    </div>
                  </div>
                  <div class="set_width" v-show="data.tv">
                    <div class="propety_row"></div>
                  </div>
                </div>
              </div>
              <!-- Kitchen -->
              <div>
                <div class="top-prodect" @click="kitchenShow = !kitchenShow">
                  <span class="title-text">Kitchen</span
                  ><i class="el-icon-arrow-down" v-if="kitchenShow"></i>
                  <i class="el-icon-arrow-right" v-if="!kitchenShow"></i>
                </div>
                <div v-if="kitchenShow">
                  <div class="check-row row">
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.bbq_tool"
                        >BBQ Tools</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.blender"
                        >Blender</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.coffee_maker"
                        >Coffee Maker</el-checkbox-button
                      >
                      <div v-show="data.coffee_maker" class="show-margin">
                        <el-form-item>
                          <p class="propety-title">Coffee Maker Type</p>
                          <el-select
                            v-model="data.coffee_maker_type"
                            placeholder="Please choose"
                            no-data-text="No data"
                          >
                            <el-option
                              v-for="item in coffeeMakerTypeList"
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.dish_or_cup_or_utensil"
                        >Dishes,Cups & Utensils</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.dishwasher"
                        >Dishwasher</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.food_processor"
                        >Food Processor</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.ice_maker"
                        >Ice Maker</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.ice_trays"
                        >Ice Trays</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.lobster_pot"
                        >Lobster Pot</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.lobster_utensil"
                        >Lobster Utensils</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.microwave"
                        >Microwave</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.oven"
                        >Oven</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.pot_or_pan"
                        >Pots and Pans</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.refrigerator"
                        >Refrigerator</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.stove"
                        >Stove</el-checkbox-button
                      >
                      <div v-show="data.stove" class="show-margin">
                        <el-form-item>
                          <p class="propety-title">Stove Type</p>
                          <el-select
                            v-model="data.stove_type"
                            placeholder="Please choose"
                            no-data-text="No data"
                          >
                            <el-option
                              v-for="item in stoveTypeList"
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.tea_kettle"
                        >Tea Kettle</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.toaster"
                        >Toaster</el-checkbox-button
                      >
                      <div v-show="data.toaster" class="show-margin">
                        <el-form-item>
                          <p class="propety-title">Toaster Type</p>
                          <el-select
                            v-model="data.toaster_type"
                            placeholder="Please choose"
                            no-data-text="No data"
                          >
                            <el-option
                              v-for="item in toastTypeList"
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!--Outdoors  -->
              <div>
                <div class="top-prodect" @click="outdoorShow = !outdoorShow">
                  <span class="title-text">Outdoors</span
                  ><i class="el-icon-arrow-down" v-if="outdoorShow"></i>
                  <i class="el-icon-arrow-right" v-if="!outdoorShow"></i>
                </div>
                <div v-if="outdoorShow">
                  <div class="check-row row">
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.beach_chair"
                        >Beach Chairs</el-checkbox-button
                      >
                      <div v-show="data.beach_chair" class="show-margin">
                        <p class="propety-title">Quantity</p>
                        <el-select
                          v-model="data.beach_chair_quantity"
                          placeholder=""
                        >
                          <el-option
                            v-for="item in 10"
                            :key="item"
                            :label="item"
                            :value="item"
                          >
                          </el-option>
                          <el-option label="10+" value="10+"></el-option>
                        </el-select>
                      </div>
                    </div>

                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.beach_towel"
                        >Beach Towels</el-checkbox-button
                      >
                      <div v-show="data.beach_towel" class="show-margin">
                        <p class="propety-title">Quantity</p>
                        <el-select
                          v-model="data.beach_towel_quantity"
                          placeholder=""
                        >
                          ><el-option
                            v-for="item in 10"
                            :key="item"
                            :label="item"
                            :value="item"
                          >
                          </el-option>
                          <el-option label="10+" value="10+"></el-option>
                        </el-select>
                      </div>
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.beach_umbrella"
                        >Beach Umbrella</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.bicycle"
                        >Bicycles</el-checkbox-button
                      >
                      <div v-show="data.bicycle" class="show-margin">
                        <el-form-item>
                          <p class="propety-title">Quantity</p>
                          <el-select
                            v-model="data.bicycle_quantity"
                            placeholder="Please choose"
                          >
                            <el-option
                              v-for="(item, index) in 100"
                              :key="index"
                              :value="index"
                              >{{ index }}</el-option
                            >
                          </el-select>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.cooler"
                        >Cooler</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.deck"
                        >Deck</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.grill"
                        >Grill / BBQ</el-checkbox-button
                      >
                      <div v-show="data.grill" class="show-margin">
                        <el-form-item>
                          <p class="propety-title">Grill Type</p>
                          <el-select
                            v-model="data.grill_type"
                            placeholder="Please choose"
                          >
                            <el-option
                              v-for="item in grillTypeList"
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.hot_tub_or_spa"
                        >Hot Tub / Spa</el-checkbox-button
                      >
                    </div>

                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.lawn_or_garden"
                        >Lawn / Garden</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.outdoor_furniture"
                        >Outdoor Furniture</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.outdoor_shower"
                        >Outdoor Shower</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.patio"
                        >Patio</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.pool"
                        >Pool</el-checkbox-button
                      >
                      <div v-show="data.pool" class="show-margin">
                        <el-form-item>
                          <p class="propety-title">Pool Type</p>
                          <el-select
                            v-model="data.pool_type"
                            placeholder="Please choose"
                          >
                            <el-option
                              v-for="item in poolTypeList"
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.porch"
                        >Porch</el-checkbox-button
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox-button v-model="data.tennis_court"
                        >Tennis Court</el-checkbox-button
                      >
                      <div v-show="data.tennis_court" class="show-margin">
                        <el-form-item>
                          <p class="propety-title">Tennis Court Type</p>
                          <el-select
                            v-model="data.tennis_court_type"
                            placeholder="Please choose"
                          >
                            <el-option
                              v-for="item in tennisCourtType"
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!--bedrooms  -->
              <div>
                <div class="top-prodect" @click="bedroomShow = !bedroomShow">
                  <span class="title-text">Bedroom</span
                  ><i class="el-icon-arrow-down" v-if="bedroomShow"></i>
                  <i class="el-icon-arrow-right" v-if="!bedroomShow"></i>
                </div>
                <div v-if="bedroomShow">
                  <div class="set_width">
                    <div class="propety_row top_row">
                      <div class="propety_type">
                        <div class="propety_row top_row">
                          <div class="propety_type">
                            <p class="propety-title">How many Bedrooms</p>
                            <el-select
                              v-model="data.bedroom_number"
                              @change="bedroomNumberChange"
                              placeholder=""
                            >
                              <el-option
                                v-for="item in 100"
                                :key="item"
                                :label="item"
                                :value="item"
                              ></el-option>
                            </el-select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div v-for="(item, index) in data.bedrooms" :key="index">
                    <div class="set_width" style="margin: 0">
                      <div>
                        <h3 class="room-small-title">
                          Bedroom&nbsp;{{ index + 1 }}
                        </h3>
                      </div>
                    </div>
                    <div class="check-row row" style="margin-top: 10px">
                      <div class="checkbox-item">
                        <el-checkbox-button v-model="item.en_suite"
                          >En suite</el-checkbox-button
                        >
                      </div>
                    </div>
                    <div class="set_width">
                      <div
                        class="propety_icon"
                        v-for="(bed, i) in item.beds"
                        :key="i"
                      >
                        <div class="propety_row">
                          <div class="propety_type">
                            <div class="propety_row">
                              <div class="propety_type">
                                <el-form-item
                                  :prop="
                                    'bedrooms.' + index + '.beds.' + i + '.type'
                                  "
                                >
                                  <p class="propety-title">Type of Bed</p>
                                  <el-select v-model="bed.type" placeholder="">
                                    <el-option
                                      v-for="item in bedTypeList"
                                      :key="item.id"
                                      :label="item.name"
                                      :value="item.id"
                                    ></el-option>
                                  </el-select>
                                </el-form-item>
                              </div>
                              <div class="propety_type">
                                <el-form-item
                                  :prop="
                                    'bedrooms.' +
                                    index +
                                    '.beds.' +
                                    i +
                                    '.number'
                                  "
                                >
                                  <p class="propety-title">Number of Beds</p>
                                  <el-select
                                    v-model="bed.number"
                                    placeholder=""
                                  >
                                    <el-option
                                      v-for="(item, index) in 10"
                                      :key="index"
                                      :label="index"
                                      :value="index"
                                    ></el-option>
                                  </el-select>
                                </el-form-item>
                              </div>
                            </div>
                          </div>
                          <div class="propety_type">
                            <div class="bedroom_row">
                              <div class="propety_level">
                                <el-form-item
                                  :prop="'bedrooms.' + index + '.floor_level'"
                                >
                                  <p class="propety-title">Floor Level</p>
                                  <el-select
                                    v-model="item.floor_level"
                                    placeholder=""
                                  >
                                    <el-option
                                      v-for="item in floorLevelList"
                                      :key="item.id"
                                      :label="item.name"
                                      :value="item.id"
                                    ></el-option>
                                  </el-select>
                                </el-form-item>
                              </div>
                              <p
                                @click="removeBed(item.beds, i)"
                                class="remove-btn"
                                v-show="i"
                              >
                                <i class="el-icon-circle-close"></i>
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="increase_btn">
                      <a class="btn-add" @click="addBed(item.beds)">+ Add</a>
                    </div>
                  </div>
                </div>
              </div>
              <!-- bathroom -->
              <div>
                <div class="top-prodect" @click="bathroomShow = !bathroomShow">
                  <span class="title-text">Bathroom</span
                  ><i class="el-icon-arrow-down" v-if="bathroomShow"></i>
                  <i class="el-icon-arrow-right" v-if="!bathroomShow"></i>
                </div>
                <div v-if="bathroomShow">
                  <div class="set_width">
                    <div class="propety_row">
                      <div class="propety_type">
                        <div class="propety_row">
                          <div class="propety_type">
                            <p class="propety-title">How many Bathrooms</p>
                            <el-select
                              v-model="data.bathroom_number"
                              @change="bathroomNumberChange"
                              placeholder="Number of bedrooms"
                            >
                              <el-option
                                v-for="item in 100"
                                :key="item"
                                :label="item"
                                :value="item"
                              ></el-option>
                            </el-select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-for="(item, index) in data.bathrooms" :key="index">
                    <div class="set_width" style="margin-top: 0">
                      <div>
                        <h3 class="room-small-title">
                          Bathroom&nbsp;{{ index + 1 }}
                        </h3>
                      </div>
                    </div>

                    <div class="check-row row" style="margin-top: 10px">
                      <div class="checkbox-item">
                        <el-checkbox-button v-model="item.bidet"
                          >Bidet</el-checkbox-button
                        >
                      </div>
                      <div class="checkbox-item">
                        <el-checkbox-button v-model="item.combination_tub"
                          >Cobination Tub</el-checkbox-button
                        >
                      </div>
                      <div class="checkbox-item">
                        <el-checkbox-button v-model="item.toilet"
                          >Toilet</el-checkbox-button
                        >
                      </div>
                      <div class="checkbox-item">
                        <el-checkbox-button v-model="item.tub"
                          >Tub</el-checkbox-button
                        >
                      </div>
                      <div class="checkbox-item">
                        <el-checkbox-button v-model="item.shower"
                          >Shower</el-checkbox-button
                        >
                      </div>
                    </div>
                    <div class="set_width">
                      <div class="propety_row">
                        <div class="propety_type">
                          <el-form-item :prop="'bathrooms.' + index + '.type'">
                            <p class="propety-title">Bathroom Type</p>
                            <el-select v-model="item.type" placeholder="">
                              <el-option
                                v-for="item in bathroomTypeList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                              ></el-option>
                            </el-select>
                          </el-form-item>
                        </div>
                      </div>
                    </div>

                    <div class="relation">
                      <el-switch
                        style="display: block"
                        v-model="item.checked"
                        active-color="#678993"
                        inactive-text="Private to a Bedroom?"
                        @change="selectBedroom(item, index)"
                      >
                      </el-switch>
                    </div>

                    <div class="set_width" v-show="item.checked">
                      <div class="propety_row">
                        <div class="propety_type">
                          <div class="propety_row">
                            <div class="propety_type">
                              <el-form-item>
                                <p class="propety-title">
                                  Associated with which Bedroom?
                                </p>
                                <el-select
                                  v-model="item.index"
                                  @change="matchBedroom(index, item.index)"
                                  placeholder=""
                                >
                                  <el-option
                                    v-for="item in bedrooms"
                                    :key="item.id"
                                    :label="item.key"
                                    :value="item.id"
                                  ></el-option>
                                </el-select>
                              </el-form-item>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 选择类型 -->
          <div class="sg_cate" v-if="$route.params.id">
            <el-radio-group v-model="action" @change="selectAction">
              <el-radio label="publish">PUBLISH</el-radio>
              <el-radio label="hide">HIDE FROM PUBLIC</el-radio>
              <el-radio label="deactivate">DEACTIVATE</el-radio>
            </el-radio-group>
          </div>
          <div class="text-reason" v-show="showReason">
            <p>Text reason for deactivation</p>
            <el-input v-model="deactivated_reason" />
          </div>
        </el-form>
      </div>
      <div class="btn-save">
        <el-button
          class="primary-button"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading"
          ><p class="row btn-row">
            <span>Save/Update</span><i class="el-icon-right"></i></p
        ></el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {};
</script>
<style lang="scss" scoped>
</style>