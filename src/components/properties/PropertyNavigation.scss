/* t基本信息 */
.infor-row{
  // dis
  -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-bottom: 10px;
}
.property-address {
  text-align: left;
  font-family: "Roboto-Bold", sans-serif;
  font-size: 20px;
  width: 300px;
  margin: 15px 0px 17px 0;
}

.homeowner {
  font-family: "Roboto-Bold", sans-serif;
  font-size: 16px;
  cursor: pointer;
  margin-right: 100px;
}

.primary-button {
  width: 150px;
}

.back {
  text-align: left;
  margin: 30px 0 20px 0;

  .el-button {
    border: none;
    background: none;
    padding: 0;
    font-family: "Roboto-Bold", sans-serif;
    text-align: left;
  }
}

.property-box {
  background-color: #fff;
  align-items: flex-start;
  box-shadow: 0 0 20px 0 rgba(17, 61, 71, 0.05);

  .content-router {
    width: calc(100% - 300px);
    min-height: calc(100vh - 100px);
    position: relative;
    &::after {
      content: "";
      border: 1px solid #e1e9ef;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
    }
  }

  .siade_right {
    width: 296px;
    font-size: 18px;
    height: 100%;

    .sr_bar {
      .exterior {
        position: relative;
        
        .sr_fo {
          text-decoration: none;
          align-items: center;
          justify-content: space-between;
          width: calc(100% - 40px);
          height: 50px;
          line-height: 50px;
          text-align: left;
          font-size: 14px;
          font-family: "Roboto-Bold", sans-serif;
          color: #404b66;
          padding: 0 20px;
          margin:0px;

          i {
            color: #678993;
            font-size: 18px;
          }

          .img {
            display: none;
          }

          &:hover {
            cursor: pointer;
          }
        }

        .router-link-active .img {
          display: block;
        }
      }
    }

    .sr_small {
      display: none;
      padding: 10px 0 10px 40px;
      text-align: left;
      position: relative;
      background: #ffffff 0% 0% no-repeat padding-box;
      border-radius: 10px 10px 0 0;

      .shownav {
        position: absolute;
        background: #ffffff 0% 0% no-repeat padding-box;
        box-shadow: 0px 10px 10px #0000000d;
        border-radius: 10px;
        width: 330px;
        left: 0px;
        top: 100px;
        z-index: 3;
        padding-left: 40px;

        .big_title {
          .router-link-active {
            &::after {
              display: none;
            }
          }

          .title_argument {
            font-size: 20px;
            font-weight: 600;
            color: #808080;
          }
        }
      }
    }

    /* 导航栏激活 */
    .router-link-active {
      color: #678993;
      background-color: #f4f9fc;
      border: #f4f9fc;
      font-family: "Roboto-Bold", sans-serif !important;

      &::after {
        content: "";
        position: absolute;
        left: 0%;
        border-left: 4px solid #678993;
        height: calc(100% - 4px);
      }
    }

  }
  .move{display: none;}

}
  @media screen and (max-width: 992px) {
  .property-box{
        display: block;
    .content-router{width: 100%;}
    .pc {
      display: none;
    }
    .move {
      display: block;
      margin-bottom: 20px;
    }
    }
    .infor-row{display: block;text-align: left;}
    .homeowner {margin: 0 0 10px 0;}
  }