@font-face {
  font-family: "Roboto-Bold";
  src: url("../fonts/Roboto/Roboto-Bold.ttf") format("truetype");
  font-style: normal;
  font-display: fallback;
}

@font-face {
  font-family: "Roboto-Medium";
  src: url("../fonts/Roboto/Roboto-Medium.ttf") format("truetype");
  font-style: normal;
  font-display: fallback;
}

@font-face {
  font-family: "Roboto-Regular";
  src: url("../fonts/Roboto/Roboto-Regular.ttf") format("truetype");
  font-style: normal;
  font-display: fallback;
}
@font-face {
  font-family: "Roboto-Light";
  src: url("../fonts/Roboto/Roboto-Light.ttf") format("truetype");
  font-style: normal;
  font-display: fallback;
}
@font-face {
  font-family: "Roboto-Italic";
  src: url("../fonts/Roboto/Roboto-Italic.ttf") format("truetype");
  font-style: normal;
  font-display: fallback;
}

@font-face {
  font-family: "Gotham-Medium";
  src: url("../fonts/Gotham/Gotham_Medium.otf") format("truetype");
  font-style: normal;
  font-display: fallback;
}

@font-face {
  font-family: "Gotham-Bold-Regular";
  src: url("../fonts/Gotham/Gotham_Bold_Regular.ttf") format("truetype");
  font-style: normal;
  font-display: fallback;
}
@font-face {
  font-family: "Poppins-Regular";
  src: url("../fonts/Popins/Poppins-Regular.ttf") format("truetype");
  font-style: normal;
  font-display: fallback;
}
.row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #678993;
  border-color: #678993;
}

.el-checkbox__input.is-checked + .el-checkbox__label {
  color: #606266;
}

.el-tooltip__popper.is-light {
  background: #678993;
  border-color: #678993;
  color: #fff;
}
.el-tooltip__popper.is-light[x-placement^="top"] .popper__arrow::after {
  border-top-color: #678993;
}
.el-textarea__inner,
.el-select-dropdown__item span,
.el-checkbox__label,
.el-popover__title {
  /* font-family: sans-serif; */
  font-family: "Roboto-Regular", sans-serif;
}
.el-checkbox__input.is-disabled + span.el-checkbox__label,
.el-input.is-disabled .el-input__inner,
.el-checkbox-button.is-disabled .el-checkbox-button__inner,
.el-range-editor.is-disabled input {
  color: #676767;
}
.el-checkbox-button.is-disabled.is-checked .el-checkbox-button__inner {
  color: #fff;
}
@media (max-width: 992px) {
  .el-message {
    top: 45% !important;
  }
}

.el-dropdown-menu .popper__arrow {
  display: none !important;
}
.el-dropdown-menu.el-popper {
  min-width: 160px;
}
iframe {
  display: none;
  visibility: hidden;
}
.v-dialog {
  min-height:50vh;
  max-height:80vh !important;
}
.v-dialog .v-card {
  border-radius: 14 px;
  padding:20px;
}
.v-list .v-list-item__content .v-list-item__title{
  text-align: left;
  font-size: 18px;
}