<template>
  <div class="payments-complete">
    <div class="content">
      <img src="../assets/icon/ico-successful.svg" alt="" />
      <p class="text-success">
        Thank you for confirming the payment disbursments.
      </p>
      <p class="text-receive">
        Payments will be made soon and you will receive confirmation.
      </p>
    </div>
  </div>
</template>
<script>
let Base64 = require("js-base64").Base64;
import {apiCreateBill} from "../API/api"
export default {
  data() {
    return {
      id: "",
      data: {},
    };
  },
  created() {
    let id = Base64.decode(this.$route.params.id);
    var arr = id.split("&");
    this.data.payment = arr[0].split("=")[1];
    this.data.timestamp = arr[1].split("=")[1];
  },
  methods:{
    init(){
      apiCreateBill(this.data).then(res=>{
      })
    }
  }
};
</script>
<style lang="scss" scoped>
.payments-complete {
  width: 100%;
  .content {
    margin: 0 auto;
    margin-top: 287px;
    // width: 387px;
    height: 210px;
    .text-success,
    .text-receive {
      font-size: 20px;
      font-family: "Roboto-Regular", sans-serif;
      color: #38425b;
      line-height: 30px;
    }
    .text-success {
      margin-top: 30px;
      font-family: "Roboto-Bold", sans-serif;
    }
  }
}
</style>